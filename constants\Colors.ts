/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * Using NativeWind with custom color palette.
 */

// Your custom color palette
export const AppColors = {
	primary: '#F74323',
	lightOrange: '#FFF5F3',
	black40: '#2F2F2F',
	bgColor: '#FEFEFE',
	grey50: '#828282',
	secondary: '#151311',
	grey40: '#B6B6B6',
	grey20: '#F3F3F3',
	grey30: '#D9D9D9',
	grey10: '#2F2F2F',
};

// Theme colors for React Navigation
const tintColorLight = AppColors.primary;
const tintColorDark = AppColors.lightOrange;

export const Colors = {
	light: {
		text: AppColors.secondary,
		background: AppColors.bgColor,
		tint: tintColorLight,
		icon: AppColors.grey50,
		tabIconDefault: AppColors.grey40,
		tabIconSelected: tintColorLight,
	},
	dark: {
		text: AppColors.lightOrange,
		background: AppColors.secondary,
		tint: tintColorDark,
		icon: AppColors.grey40,
		tabIconDefault: AppColors.grey50,
		tabIconSelected: tintColorDark,
	},
};
