/** @type {import('tailwindcss').Config} */
module.exports = {
	// NOTE: Update this to include the paths to all files that contain Nativewind classes.
	content: [
		'./App.tsx',
		'./app/**/*.{js,jsx,ts,tsx}',
		'./components/**/*.{js,jsx,ts,tsx}',
		'./providers/**/*.{js,jsx,ts,tsx}',
		'./hooks/**/*.{js,jsx,ts,tsx}',
		'./constants/**/*.{js,jsx,ts,tsx}',
		'./types/**/*.{js,jsx,ts,tsx}',
		'./services/**/*.{js,jsx,ts,tsx}',
		'./stores/**/*.{js,jsx,ts,tsx}',
	],
	presets: [require('nativewind/preset')],
	theme: {
		extend: {
			colors: {
				primary: '#F74323',
				'light-orange': '#FFF5F3',
				'black-40': '#2F2F2F',
				'bg-color': '#FEFEFE',
				'grey-50': '#828282',
				secondary: '#151311',
				'grey-40': '#B6B6B6',
				'grey-20': '#F3F3F3',
				'grey-30': '#D9D9D9',
				'grey-10': '#2F2F2F',
			},
		},
	},
	plugins: [],
};
