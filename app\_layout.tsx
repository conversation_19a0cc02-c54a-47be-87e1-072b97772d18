import {
	DarkTheme,
	De<PERSON>ult<PERSON>heme,
	ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import './globals.css';

import { useColorScheme } from '@/hooks/useColorScheme';
import { ApiAuthSync } from '@/providers/api-auth';
import { ReactQueryProvider } from '@/providers/react-query';

function RootLayoutNav() {
	return (
		<Stack screenOptions={{ headerShown: false }}>
			{true ? (
				<Stack.Screen
					name='(tabs)'
					options={{ headerShown: false }}
				/>
			) : (
				<Stack.Screen
					name='auth'
					options={{ headerShown: false }}
				/>
			)}
		</Stack>
	);
}

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [loaded] = useFonts({
		SpaceMono: require('../assets/fonts/Inter-VariableFont_opsz,wght.ttf'),
	});

	if (!loaded) {
		// Async font loading only occurs in development.
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
			<ReactQueryProvider>
				<ApiAuthSync />
				<RootLayoutNav />
				<StatusBar style='auto' />
			</ReactQueryProvider>
		</ThemeProvider>
	);
}
