import React from 'react';
import { View, Text, ScrollView } from 'react-native';

/**
 * Example component showing how to use your custom colors with NativeWind
 */
export function ColorExample() {
  return (
    <ScrollView className="flex-1 bg-bg-color p-4">
      <Text className="text-2xl font-bold text-secondary mb-6">
        Your Custom Color Palette
      </Text>
      
      {/* Primary Colors */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-secondary mb-3">Primary Colors</Text>
        
        <View className="bg-primary p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Primary (#F74323)</Text>
        </View>
        
        <View className="bg-secondary p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Secondary (#151311)</Text>
        </View>
        
        <View className="bg-light-orange p-4 rounded-lg mb-2 border border-grey-30">
          <Text className="text-secondary font-medium">Light Orange (#FFF5F3)</Text>
        </View>
      </View>
      
      {/* Grey Scale */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-secondary mb-3">Grey Scale</Text>
        
        <View className="bg-grey-10 p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Grey 10 (#2F2F2F)</Text>
        </View>
        
        <View className="bg-grey-20 p-4 rounded-lg mb-2">
          <Text className="text-secondary font-medium">Grey 20 (#F3F3F3)</Text>
        </View>
        
        <View className="bg-grey-30 p-4 rounded-lg mb-2">
          <Text className="text-secondary font-medium">Grey 30 (#D9D9D9)</Text>
        </View>
        
        <View className="bg-grey-40 p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Grey 40 (#B6B6B6)</Text>
        </View>
        
        <View className="bg-grey-50 p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Grey 50 (#828282)</Text>
        </View>
      </View>
      
      {/* Background Colors */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-secondary mb-3">Background Colors</Text>
        
        <View className="bg-bg-color p-4 rounded-lg mb-2 border border-grey-30">
          <Text className="text-secondary font-medium">Background Color (#FEFEFE)</Text>
        </View>
        
        <View className="bg-black-40 p-4 rounded-lg mb-2">
          <Text className="text-white font-medium">Black 40 (#2F2F2F)</Text>
        </View>
      </View>
      
      {/* Usage Examples */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-secondary mb-3">Usage Examples</Text>
        
        {/* Button Example */}
        <View className="bg-primary p-4 rounded-lg mb-3 items-center">
          <Text className="text-white font-semibold text-base">Primary Button</Text>
        </View>
        
        {/* Card Example */}
        <View className="bg-light-orange p-4 rounded-lg border border-grey-30 mb-3">
          <Text className="text-secondary font-semibold text-base mb-2">Card Title</Text>
          <Text className="text-grey-50 text-sm">
            This is an example card using your light orange background with grey text.
          </Text>
        </View>
        
        {/* Input Example */}
        <View className="bg-white p-4 rounded-lg border border-grey-30 mb-3">
          <Text className="text-grey-50 text-sm mb-1">Label</Text>
          <Text className="text-secondary text-base">Input placeholder text</Text>
        </View>
      </View>
    </ScrollView>
  );
}
