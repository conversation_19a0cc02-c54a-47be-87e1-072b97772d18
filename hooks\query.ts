import { useMutation, useQuery, UseMutationOptions, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';

// Thin wrappers to centralize common options/types for the app
export function useAppQuery<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(
  options: UseQueryOptions<TQueryFnData, TError, TData>
): UseQueryResult<TData, TError> {
  return useQuery({
    retry: 1,
    staleTime: 1000 * 60 * 2,
    refetchOnWindowFocus: false,
    ...options,
  });
}

export function useAppMutation<TData = unknown, TError = unknown, TVariables = void, TContext = unknown>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>
) {
  return useMutation(options);
}

