import { create } from 'zustand';

export type AuthState = {
  token?: string;
  isAuthenticated: boolean;
};

export type AuthActions = {
  setToken: (token?: string) => void;
  logout: () => void;
};

const initialState: AuthState = {
  token: undefined,
  isAuthenticated: false,
};

export const useAuthStore = create<AuthState & AuthActions>((set) => ({
  ...initialState,
  setToken: (token) =>
    set(() => ({ token, isAuthenticated: Boolean(token) })),
  logout: () => set(() => ({ token: undefined, isAuthenticated: false })),
}));

